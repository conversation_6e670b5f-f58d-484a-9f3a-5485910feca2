[package]
name = "moduforge-macros"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 宏定义"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_macro"
path="./src/lib.rs"

[dependencies]
moduforge-model = { workspace = true }
moduforge-state ={ workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
anyhow = {workspace=true}
async-trait = {workspace=true}

