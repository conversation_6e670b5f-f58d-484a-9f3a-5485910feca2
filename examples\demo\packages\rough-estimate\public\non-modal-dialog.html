<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>非模态弹窗 - 概算工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h2 {
            margin-bottom: 8px;
            font-size: 22px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .content {
            flex: 1;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .tool-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #52c41a;
        }

        .tool-info h4 {
            margin-bottom: 8px;
            color: #52c41a;
        }

        .tool-info p {
            font-size: 13px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .tool-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tool-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .tool-card .icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .tool-card h4 {
            margin-bottom: 8px;
            font-size: 16px;
        }

        .tool-card p {
            font-size: 12px;
            opacity: 0.8;
        }

        .calculator {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .calculator h4 {
            margin-bottom: 15px;
            text-align: center;
        }

        .calc-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }

        .calc-row label {
            min-width: 80px;
            font-size: 14px;
        }

        .calc-row input {
            flex: 1;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .calc-row input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .calc-result {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
            margin-top: 10px;
        }

        .actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #52c41a;
            color: white;
        }

        .btn-primary:hover {
            background: #73d13d;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🔧 非模态弹窗</h2>
        <p>此弹窗不会阻止主窗口操作，可以同时使用</p>
    </div>

    <div class="content">
        <div class="tool-info">
            <h4>非模态窗口特性</h4>
            <p>• 主窗口保持可操作状态<br>
               • 可以同时打开多个非模态窗口<br>
               • 适用于工具面板、辅助功能等场景</p>
        </div>

        <div class="tools-grid">
            <div class="tool-card" onclick="openCalculator()">
                <span class="icon">🧮</span>
                <h4>快速计算器</h4>
                <p>概算金额计算</p>
            </div>
            <div class="tool-card" onclick="openConverter()">
                <span class="icon">🔄</span>
                <h4>单位转换</h4>
                <p>工程量单位转换</p>
            </div>
            <div class="tool-card" onclick="openNotes()">
                <span class="icon">📝</span>
                <h4>备忘录</h4>
                <p>项目备注记录</p>
            </div>
            <div class="tool-card" onclick="openHistory()">
                <span class="icon">📊</span>
                <h4>历史记录</h4>
                <p>查看操作历史</p>
            </div>
        </div>

        <div class="calculator" id="calculator" style="display: none;">
            <h4>💰 概算计算器</h4>
            <div class="calc-row">
                <label>工程量:</label>
                <input type="number" id="quantity" placeholder="请输入工程量" oninput="calculate()">
            </div>
            <div class="calc-row">
                <label>单价:</label>
                <input type="number" id="unitPrice" placeholder="请输入单价" oninput="calculate()">
            </div>
            <div class="calc-row">
                <label>系数:</label>
                <input type="number" id="coefficient" value="1" oninput="calculate()">
            </div>
            <div class="calc-result" id="result">
                总金额: 0.00 元
            </div>
        </div>
    </div>

    <div class="actions">
        <button class="btn btn-secondary" onclick="closeWindow()">关闭</button>
        <button class="btn btn-primary" onclick="refreshTools()">刷新工具</button>
    </div>

    <script>
        // 打开计算器
        function openCalculator() {
            const calc = document.getElementById('calculator');
            calc.style.display = calc.style.display === 'none' ? 'block' : 'none';
        }

        // 计算总金额
        function calculate() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const unitPrice = parseFloat(document.getElementById('unitPrice').value) || 0;
            const coefficient = parseFloat(document.getElementById('coefficient').value) || 1;
            
            const total = quantity * unitPrice * coefficient;
            document.getElementById('result').textContent = `总金额: ${total.toFixed(2)} 元`;
        }

        // 其他工具功能
        function openConverter() {
            alert('单位转换工具开发中...');
        }

        function openNotes() {
            alert('备忘录功能开发中...');
        }

        function openHistory() {
            alert('历史记录功能开发中...');
        }

        function refreshTools() {
            console.log('刷新工具面板');
            alert('工具面板已刷新！');
        }

        // 关闭窗口
        function closeWindow() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrentWindow().close();
            } else {
                window.close();
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            console.log('非模态弹窗页面已加载');
        });
    </script>
</body>
</html>
