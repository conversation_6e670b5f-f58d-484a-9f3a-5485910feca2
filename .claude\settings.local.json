{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "Bash(npm run build:*)", "Bash(cargo check:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cargo test)", "Bash(cargo test:*)", "<PERSON><PERSON>(sed:*)", "Bash(timeout 60 cargo test --test integration_tests)", "Bash(cargo run:*)", "Bash(rustc:*)", "Bash(timeout 60 cargo test -p moduforge-macros-derive)", "Bash(timeout 30 cargo test -p moduforge-macros-derive integration_tests)", "Bash(timeout 30 cargo test -p moduforge-macros-derive)", "Bash(cargo build:*)", "Bash(npm run dev:*)"], "deny": [], "ask": [], "additionalDirectories": ["D:\\mnt\\d\\workspace\\rust2025\\moduforge-rs\\crates", "D:\\d\\workspace\\rust2025\\moduforge-rs\\crates", "D:\\d\\workspace\\rust2025\\moduforge-rs\\packages"]}}