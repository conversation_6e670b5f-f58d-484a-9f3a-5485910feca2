[package]
name = "demo-app"
version = "0.1.0"
description = "md 框架的demo 演示"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.71"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "lib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
log = "0.4"
tauri = { version = "2.5.1", features = [ "tray-icon"] }
tauri-plugin-log = "2.5.0"
tauri-plugin-dialog = "2.0.0"
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7.14"
serde = { version = "1.0", features = ["derive", "rc"] }
serde_json = "1.0"
state= {version = "0.6.0", features = ["tls"] } # 状态管理
async-trait="0.1.88"     
dashmap = "6.1.0"   #

rand = "0.9.1"
# lazy_static 线程安全全局变量
lazy_static = "1.4"
anyhow="1"
axum = { version = "0.8.4", features = ["json"] }
tower-http = { version = "0.5", features = ["cors"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"
# 核心模块
moduforge-model =  {workspace = true}
moduforge-state =    {workspace = true}
moduforge-transform = {workspace = true}
moduforge-core = {workspace = true}
moduforge-macros = {workspace = true}
moduforge-macros-derive = {workspace = true}
moduforge-collaboration-client = {workspace = true}

# 规则引擎
moduforge-rules-engine = {workspace = true}
moduforge-rules-expression = {workspace = true}
moduforge-rules-template = {workspace = true}
chrono = "0.4.41"

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2.2.1"
