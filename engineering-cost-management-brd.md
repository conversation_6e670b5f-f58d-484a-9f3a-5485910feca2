# 工程计价与成本管理软件业务需求文档 (BRD)

**文档版本**: 1.0  
**创建日期**: 2025/08/28  
**目标框架**: ModuForge-RS  

## 1. 项目愿景 (Project Vision)

打造一款基于 ModuForge-RS 框架的现代化、高效率、数据驱动的工程计价与成本管理协同平台。该平台旨在打通工程项目从前期投资估算、设计概算、施工图预算、过程成本控制、竣工结算到最终审计的全链路，实现数据同源、过程可溯、协同高效，为工程造价行业提供精准、可靠的数字化解决方案。

## 2. 核心目标 (Core Objectives)

### 2.1 业务目标
- **提升编制效率**: 通过智能化的定额库、指标库和价格库，自动化完成大量重复计算工作
- **保证数据准确性**: 建立统一的数据标准和计算规则，避免因人为因素导致的计算错误
- **强化过程管控**: 实现预算数据与实际成本数据的动态对比分析
- **促进团队协同**: 支持多用户在同一项目内进行分工协作、实时沟通和版本管理
- **实现全流程覆盖**: 无缝衔接"概、预、结、审"各个阶段

### 2.2 技术目标
- **充分利用 ModuForge-RS 优势**: 基于 Node 树形结构完美映射工程预算层级
- **事务性数据一致性**: 利用 Transaction 系统确保计价操作的原子性和可回滚
- **插件化扩展能力**: 通过插件系统支持不同地区定额标准和计价规范
- **实时协作支持**: 基于 CRDT 技术实现多用户实时协作编辑

## 3. 用户画像 (User Personas)

### 3.1 造价工程师 (Cost Engineer)
- **核心诉求**: 快速、准确地完成工程量清单计价、定额套用、材料价格调整
- **日常工作**: 新建项目、导入图纸/清单、编制预算、调整价格、输出报表
- **技能水平**: 熟悉造价专业知识，对软件操作有一定要求

### 3.2 项目经理 (Project Manager)
- **核心诉求**: 宏观掌握项目成本状况，审批预算、变更和结算
- **日常工作**: 审核预算文件、跟踪成本动态、审批流程、查看项目看板
- **技能水平**: 管理导向，需要直观的数据展示和决策支持

### 3.3 审计人员 (Auditor)
- **核心诉求**: 追溯每一笔费用的来源和计算过程，进行数据比对
- **日常工作**: 审查结算文件、对比预算与结算差异、标记审计问题
- **技能水平**: 专业审计背景，需要强大的数据追溯和对比功能

## 4. 核心功能模块 (Core Functional Modules)

### 4.1 项目管理模块
**基于 ModuForge-RS 的实现优势**:
- 利用 Node 系统的层级结构管理项目组织架构
- 通过 Mark 系统标记项目状态和权限
- 基于 Transaction 确保项目操作的原子性

**关键特性**:
- 项目创建、打开、保存、归档的完整生命周期管理
- 基于角色的权限控制（编制人、审核人、管理员）
- 项目模板功能，支持快速创建新项目
- 项目基础信息管理（建设单位、施工单位、工程地点等）

### 4.2 概预算编制模块 (核心模块)
**ModuForge-RS 映射设计**:
```
预算文档 (BudgetDocument)
├── 工程项目 (Project)
│   ├── 单位工程 (UnitProject)
│   │   ├── 分部工程 (Division)
│   │   │   ├── 分项工程 (SubDivision)
│   │   │   │   └── 清单项目 (ListItem)
│   │   │   └── 措施项目 (MeasureItem)
│   │   └── 其他项目 (OtherItem)
│   └── 规费税金 (FeesAndTaxes)
```

**关键特性**:
- **分部分项工程**: 
  - 基于 Node 树形结构的项目层级管理
  - 支持导入/导出标准格式的工程量清单
  - 清单项与定额子目的智能关联和套用
  - 工程量计算底稿，支持公式化输入和引用计算
- **措施项目**: 支持按费率或按项计算两种模式
- **其他项目**: 包含暂列金额、暂估价、计日工、总承包服务费等
- **规费与税金**: 根据地区和工程类型自动应用相应费率
- **多版本管理**: 基于 Transaction 的版本控制和历史追溯

### 4.3 结算与审核模块
**基于 ModuForge-RS 的实现优势**:
- 利用 Transaction 系统实现变更操作的可追溯性
- 通过 Event 系统实现变更通知和自动计算
- 基于 Mark 系统标记审核状态和问题

**关键特性**:
- **变更管理**: 新增、修改、删除变更项，自动计算费用增减
- **结算编制**: 在预算基础上进行调整，形成结算书
- **对比分析**: 自动生成预算与结算的详细对比表
- **审核流程**: 内置审核工作流，支持多级审核和在线标记

### 4.4 工料机管理模块
**关键特性**:
- **价差调整**: 批量或单独调整工料机单价，自动重新计算项目总价
- **市场价/信息价**: 支持导入不同时期的市场价格信息
- **资源分析**: 自动汇总项目所需的工料机资源总量
- **甲供材管理**: 标记甲供材料，在总价中进行相应扣除

### 4.5 基础数据库模块
**基于 ModuForge-RS 的插件化设计**:
- 通过插件系统支持不同地区的定额标准
- 利用 Schema 系统定义数据结构和验证规则
- 基于 Node 系统统一管理各类基础数据

**关键特性**:
- **定额库**: 支持国家、行业及地方的消耗量定额和预算定额
- **费用库**: 内置各地区的费用标准文件
- **价格库**: 定期更新的材料信息价库
- **指标库**: 沉淀历史项目数据，形成各类工程的造价指标

### 4.6 报表中心模块
**关键特性**:
- 内置标准报表模板（封面、总说明、投标总价、工程项目总价表等）
- 支持自定义报表布局和内容
- 支持导出为 PDF、Excel 等多种格式
- 基于 Expression 系统的动态报表计算

## 5. 技术架构建议

### 5.1 核心框架选择
**强烈推荐使用 ModuForge-RS 框架**，理由如下：

1. **完美的业务模型映射**:
   - Node 的树形结构天然契合工程预算的层级结构
   - Attrs 系统承载业务数据（价格、数量、单位等）
   - Mark 系统记录业务状态（计算状态、锁定状态、审核状态等）

2. **强大的事务保障**:
   - Transaction 系统确保计价操作的原子性和数据一致性
   - 支持撤销/重做操作，提升用户体验
   - 批量操作优化，提高大型项目处理性能

3. **优秀的扩展性**:
   - 插件化架构支持不同地区定额标准的灵活接入
   - Extension 系统支持自定义业务逻辑
   - 中间件模式支持横切关注点处理

4. **实时协作能力**:
   - 基于 CRDT 的冲突自由协作
   - 支持多用户实时编辑和同步
   - WebSocket 实时通信支持

### 5.2 技术栈规划
- **后端核心**: ModuForge-RS + Rust + Tokio
- **规则引擎**: mf-engine (支持动态计价规则)
- **表达式计算**: mf-expression (支持复杂公式计算)
- **数据持久化**: mf-persistence + PostgreSQL
- **实时协作**: mf-collaboration (基于 Yrs CRDT)
- **前端技术**: React/Vue + TypeScript
- **部署方式**: 云原生架构（SaaS）

## 6. 非功能性需求

### 6.1 性能要求
- 支持处理超过10万条清单项的大型项目
- 价格调整、费用重算等操作应在秒级响应
- 支持并发用户数不少于100人

### 6.2 可靠性要求
- 所有操作均支持撤销/重做
- 提供自动保存和数据备份机制
- 系统可用性不低于99.5%

### 6.3 安全性要求
- 严格的权限控制，确保数据安全
- 关键操作的完整操作日志记录
- 数据传输和存储加密

### 6.4 可扩展性要求
- 支持通过插件形式增加新功能
- 提供二次开发接口（API）
- 支持与企业内部其他系统集成

## 7. 项目优势总结

基于 ModuForge-RS 框架开发工程计价软件具有以下核心优势：

1. **架构优势**: Node 模型与工程预算业务的天然匹配
2. **技术优势**: 事务性操作保证数据一致性和可靠性
3. **性能优势**: 基于 Rust 的高性能异步处理能力
4. **扩展优势**: 插件化架构支持灵活的业务扩展
5. **协作优势**: 内置的实时协作能力提升团队效率

这份业务需求文档为 `steering-architect` Agent 提供了完整的项目蓝图，可以直接用于生成 `product.md`、`tech.md` 和 `structure.md` 等核心指导文件。
