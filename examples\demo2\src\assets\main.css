@import './base.css';

/* 主题变量 */
:root {
  /* Element Plus 浅色主题变量会自动生效 */
}

/* 手动深色主题 */
[data-theme="dark"] {
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
  
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-error: #f56c6c;
  --el-color-info: #909399;
  
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #121212;
  --el-bg-color-overlay: #1d1e1f;
  
  --el-text-color-primary: #ffffff;
  --el-text-color-regular: #cfcfcf;
  --el-text-color-secondary: #a8a8a8;
  --el-text-color-placeholder: #8d8d8d;
  --el-text-color-disabled: #6c6c6c;
  
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-darker: #636466;
  
  --el-fill-color: #2d2d2d;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: transparent;
  
  --el-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --el-box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.2);
  --el-box-shadow-lighter: 0 1px 2px rgba(0, 0, 0, 0.1);
  --el-box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 手动浅色主题 */
[data-theme="light"] {
  /* Element Plus 默认浅色主题变量会自动生效 */
}

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 通用样式 */
body {
  margin: 0;
  padding: 0;
  background: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}
